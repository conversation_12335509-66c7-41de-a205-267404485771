import EXIF from 'exif-js'
import {extend} from '@/utils/object-util.js'

const _object = {
  readFilesToBase64String (files) {
    return new Promise(resolve => {
      const _files = []

      if (!Array.isArray(files) || files.length === 0) {
        resolve(_files)

        return
      }

      let _count = 0
      const _reader = new FileReader()

      files.forEach(i => {
        _reader.readAsDataURL(i)
        _reader.onload = function (e) {
          _files.push({
            name: i.name,
            base64: this.result
          })

          _count++

          if (_count === files.length) {
            resolve(_files)
          }
        }
      })
    })
  },
  downloadFromBase64 (name, mime, base64) {
    const _atob = atob(base64)
    let _length = _atob.length
    const _array = new Uint8Array(_length)
    while (_length--) {
      _array[_length] = _atob.charCodeAt(_length)
    }

    const _blob = new Blob([_array], {
      type: mime
    })
    const _url = URL.createObjectURL(_blob)

    const _el = document.createElement('a')
    _el.setAttribute('href', _url)
    _el.setAttribute('download', name)
    _el.setAttribute('target', '_blank')

    const _event = document.createEvent('MouseEvents')
    _event.initEvent('click', true, true)

    _el.dispatchEvent(_event)
  },
  copyUrlToFile (url, name) {
    const that = this

    const _xhr = new XMLHttpRequest()
    _xhr.responseType = 'blob'
    _xhr.onload = function (e) {
      if (this.status === 200) {
        const _blob = this.response
        _blob.type = 'application/octet-stream'

        that.downloadBlob(_blob, name)
      }
    }

    _xhr.open('GET', url, true)
    _xhr.send()
  },
  downloadBlob (blob, name) {
    const _a = document.createElement('a')
    _a.href = URL.createObjectURL(blob)
    _a.download = name
    _a.click()
  },

  // 判断文件后缀是否为图片
  isImage (name) {
    if (typeof name !== 'string') {
      return false
    }

    const _index = name.lastIndexOf('.')
    const _ext = name.substr(_index + 1).toLowerCase()
    return ['bmp', 'cdr', 'dxf', 'exif', 'fpx', 'gif', 'jpeg', 'jpg', 'pcd', 'pcx', 'png', 'psd', 'svg', 'tga', 'tif'].indexOf(_ext) !== -1
  },

  // 缩放图片
  zoomImage (file, options) {
    return new Promise(resolve => {
      const _options = {
        max: 1080,
        format: 'image/jpeg'
      }
      extend(true, _options, options)

      const _canvas = document.createElement('canvas')
      const _ctx = _canvas.getContext('2d')

      const _img = new Image()
      _img.onload = function () {
        let w = this.naturalWidth
        let h = this.naturalHeight

        // 缩放图片
        if (Number.isFinite(_options.max)) {
          if (w !== h * 2 && h !== w * 2 && (w > _options.max || h > _options.max)) { // 非全景图，并且超高
            if (w > h) {
              const _zoom = w / _options.max
              w = _options.max
              h = h / _zoom
            } else {
              const _zoom = h / _options.max
              h = _options.max
              w = w / _zoom
            }
          }
        }

        _canvas.width = w
        _canvas.height = h

        // 摆正图片
        let _orientation = null
        EXIF.getData(file, function () {
          _orientation = EXIF.getTag(this, 'Orientation')
        })

        let x = 0
        let y = 0

        if (_orientation !== '' && _orientation !== 1) {
          let _rotateTimes
          switch (_orientation) {
            case 3: // 旋转180
              _rotateTimes = 2
              break
            case 6: // 顺时针旋转90
              _rotateTimes = 1
              break
            case 8: // 逆时针旋转90
              _rotateTimes = 3
              break
          }

          w = _canvas.width
          h = _canvas.height
          switch (_rotateTimes) {
            case 1:
              _canvas.width = h
              _canvas.height = w
              y = -h

              break
            case 2:
              x = -w
              y = -h

              break
            case 3:
              _canvas.width = h
              _canvas.height = w

              x = -w
              break
          }
          const _degree = Math.PI / 180 * 90 * _rotateTimes
          _ctx.rotate(_degree)
        }

        _ctx.clearRect(0, 0, _canvas.width, _canvas.height)
        _ctx.drawImage(_img, x, y, _canvas.width, _canvas.height)

        resolve(_canvas.toDataURL(_options.format, 100))
      }

      const _reader = new FileReader()
      _reader.readAsDataURL(file)
      _reader.onload = function (e) {
        _img.src = this.result
      }
    })
  }
}

_object.copyInputStreamToFile = function (bytes, name) {
  const _blob = new Blob([bytes], {
    type: 'application/octet-stream'
  })

  this.downloadBlob(_blob, name)
}

export default _object
