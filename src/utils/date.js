Date.today = () => {
  const _date = new Date()
  return new Date(_date.getFullYear(), _date.getMonth(), _date.getDate())
}

Date.lastMonday = date => {
  const _date = date instanceof Date ? new Date(date.getFullYear(), date.getMonth(), date.getDate()) : new Date()
  _date.setDate(_date.getDate() - _date.getDay() + 1)
  return new Date(_date.getFullYear(), _date.getMonth(), _date.getDate())
}

Date.lastWeekdays = () => {
  return Date.lastNDays(7)
}

Date.thisWeekdays = () => {
  let _date = new Date()
  const _weekday = _date.getDay() - 1
  _date = _date.addDays(_weekday * -1)

  const _array = []
  for (let i = 0; i < 7; i++) {
    _date = i === 0 ? _date : _date.addDays(1)
    _array.push(_date.format('yyyy-MM-dd'))
  }
  return _array
}

Date.lastNDays = num => {
  const _array = []
  for (let i = 0, date = new Date().addDays(-num); i < num; i++) {
    date = date.addDays(1)
    _array.push(date.format('yyyy-MM-dd'))
  }

  return _array
}

Date.prototype.format = function (str) {
  const _pattens = {
    'M+': this.getMonth() + 1, // 月份
    'd+': this.getDate(), // 日
    'h+': this.getHours() % 12 === 0 ? 12 : this.getHours() % 12, // 小时
    'H+': this.getHours(), // 小时
    'm+': this.getMinutes(), // 分
    's+': this.getSeconds(), // 秒
    'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds() // 毫秒
  }

  if (/(y+)/.test(str)) {
    str = str.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
  }

  for (const i in _pattens) {
    if (new RegExp('(' + i + ')').test(str)) {
      str = str.replace(RegExp.$1, (RegExp.$1.length === 1) ? (_pattens[i]) : (('00' + _pattens[i]).substr(('' + _pattens[i]).length)))
    }
  }

  return str
}

Date.prototype.addYears = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setFullYear(_date.getFullYear() + num)
  return _date
}

Date.prototype.addDays = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setDate(_date.getDate() + num)
  return _date
}

Date.prototype.addHours = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setHours(_date.getHours() + num)
  return _date
}

Date.prototype.addMinutes = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setMinutes(_date.getMinutes() + num)
  return _date
}

Date.prototype.addSeconds = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setSeconds(_date.getSeconds() + num)
  return _date
}

Date.prototype.addMilliseconds = function (num) {
  const _date = new Date()
  _date.setTime(this.getTime())
  _date.setMilliseconds(_date.getMilliseconds() + num)
  return _date
}

Date.prototype.diff = function (time) {
  const _diff = this - new Date(time).getTime()

  return {
    days: parseInt(_diff / (24 * 60 * 60 * 1000) + ''),
    hours: parseInt(_diff % (24 * 60 * 60 * 1000) / (60 * 60 * 1000) + ''),
    minutes: parseInt(_diff % (60 * 60 * 1000) / (60 * 1000) + '')
  }
}
