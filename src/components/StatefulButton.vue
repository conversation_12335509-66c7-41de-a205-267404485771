<script setup>
import {defineProps, ref} from 'vue'
import GenericIcon from '@/components/GenericIcon.vue'
import {isFunction} from '@/utils/object-util.js'

const props = defineProps({
  // 图标
  // eslint-disable-next-line vue/require-default-prop
  icon: {
    type: String
  },
  // 禁用时显示为禁用或者加载中
  loadingStyle: {
    type: String,
    default: 'loading'
  },
  onClick: {
    type: Function,
    default: () => Promise.resolve()
  }
})

// 点击按钮时切换为加载状态
const disabled = ref(false)
const onClick = isFunction(props.onClick)
  ? event => {
    disabled.value = true

    const _returnVal = props.onClick(event)
    if (_returnVal instanceof Promise) {
      _returnVal.finally(() => {
        disabled.value = false
      })
    } else {
      disabled.value = false
    }
  }
  : null
</script>

<template>
  <a-button
    v-bind="$attrs"
    :disabled="props.loadingStyle === 'disabled' && disabled"
    :loading="props.loadingStyle === 'loading' && disabled"
    @click="onClick"
  >
    <!-- 图标 -->
    <template #icon>
      <GenericIcon :icon="props.icon" />
    </template>

    <!-- 其它插槽 -->
    <template
      v-for="(index, name) in $slots"
      #[name]
    >
      <slot :name="name" />
    </template>
  </a-button>
</template>
