<script setup>
import {computed, ref, watch} from 'vue'

const props = defineProps({
  accept: {
    type: String,
    default: '*'
  },
  component: {
    type: String,
    default: 'dragger'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  label: {
    type: String,
    default: '上传'
  },
  max: {
    type: Number,
    default: 1
  },
  showUploadList: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      showPreviewIcon: false,
      showRemoveIcon: true,
      showDownloadIcon: true
    }
  },
  upload: {
    type: Function,
    default: file => {
      console.log(file)

      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject({
        message: '未提供upload方法'
      })
    }
  }
})

const accept = computed(() => {
  return props.component === 'gallery' ? 'image/*' : props.accept
})

const listType = computed(() => {
  return props.component === 'gallery' ? 'picture-card' : 'text'
})

const files = ref([])

const getFiles = () => {
  return files.value
    .filter(i => ['done', 'success'].indexOf(i.status) !== -1)
    .map(i => {
      return {
        id: i._id,
        url: i.url
      }
    })
}

const setFiles = obj => {
  files.value = obj === null
    ? []
    : obj.files.map(i => {
      return {
        uid: i.id,
        _id: i.id,
        name: i.name,
        url: i.url,
        status: 'done'
      }
    })
}

defineExpose({
  getFiles,
  setFiles
})

const upload = ({ file }) => {
  const _promise = props.upload(file)

  _promise
    .then(data => {
      files.value
        .filter(i => i.uid === file.uid)
        .forEach(i => {
          i._id = data.id
          i.url = data.url
          i.status = 'done'
        })
    })
    .catch(error => {
      files.value
        .filter(i => i.uid === file.uid)
        .forEach(i => {
          i.status = 'error'

          // 设置错误信息
          i.response = error.message || error.code
        })
    })
}

const emits = defineEmits(['download', 'change'])

watch(
  () => files.value,
  val => {
    emits('change', val.filter(i => ['done', 'success'].indexOf(i.status) !== -1))
  }, {
    deep: true
  }
)

const download = file => {
  emits('download', file)
}
</script>

<template>
  <template v-if="props.component === 'dragger'">
    <a-upload-dragger
      v-model:file-list="files"
      :accept="accept"
      :custom-request="upload"
      :disabled="props.disabled"
      :list-type="listType"
      :max-count="props.max"
      :multiple="props.max > 1"
      :show-upload-list="props.showUploadList"
      :with-credentials="true"
      @download="download"
    >
      <p class="ant-upload-drag-icon">
        <inbox-outlined />
      </p>
      <p class="ant-upload-text">
        {{ props.label }}
      </p>
      <p class="ant-upload-hint">
        Support for a single or bulk upload. Strictly prohibit from uploading company data or other
        band files
      </p>
    </a-upload-dragger>
  </template>
  <template v-else>
    <a-upload
      v-model:file-list="files"
      :accept="accept"
      :custom-request="upload"
      :disabled="props.disabled"
      :list-type="listType"
      :max-count="props.max"
      :multiple="props.max > 1"
      :show-upload-list="props.showUploadList"
      :with-credentials="true"
      @download="download"
    >
      <template v-if="props.component === 'gallery'">
        <div v-if="files.length === 0 || props.multiple">
          <plus-outlined />
          <div style="margin-top: 8px">
            {{ props.label }}
          </div>
        </div>
      </template>
      <template v-else>
        <a-button>
          <upload-outlined />
          {{ props.label }}
        </a-button>
      </template>
    </a-upload>
  </template>
</template>

<style scoped lang="less">

</style>
