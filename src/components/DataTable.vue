<script setup>
import {computed, defineProps, onMounted, ref} from 'vue'
import dayjs from 'dayjs'
import GenericIcon from '@/components/GenericIcon.vue'
import StatefulButton from '@/components/StatefulButton.vue'
import FeedbackUtil from '@/utils/feedback.js'
import {extend, isEmptyObject, isFunction} from '@/utils/object-util.js'

const props = defineProps({
  // 默认属性
  columns: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  multiple: {
    type: Boolean,
    default: false
  },
  pagination: {
    type: Object,
    default: null
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  size: {
    type: String,
    default: 'small'
  },

  // 自定义属性
  // ACTION
  actions: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  // 工具栏
  toolbar: {
    type: Array,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: []
  },
  // 查询
  load: {
    type: Function,
    default: (count, index, sorters) => Promise.resolve({
      total: 0,
      records: []
    })
  },
  // 初始化后加载
  loadAfterInitialized: {
    type: Boolean,
    default: true
  }
})

let selectedRecords = []
const getSelectedRecords = () => {
  return selectedRecords
}

const selected = (keys, records) => {
  selectedRecords = [...records]
}

// 表头定义
const columns = []

// 表头设置数据列
props.columns.forEach(i => {
  // 列定义设置默认值
  const _column = {
    // 居中对齐
    align: 'center',
    // 超长自动省略
    ellipsis: true,
    // 不支持伸缩
    resizable: false,
    // 类型
    type: 'text',
    // 支持排序
    sorter: false
  }

  extend(true, _column, i)

  // 重置参数
  switch (_column.type) {
    case 'datetime':
      if (!_column.config) {
        _column.config = {}
      }

      if (!_column.config.format) {
        _column.config.format = 'YYYY-MM-DD HH:mm:ss'
      }

      break
    case 'select':
      if (!_column.config) {
        _column.config = {}
      }

      if (!Array.isArray(_column.config.options)) {
        _column.config.options = []
      }

      break
  }

  if (_column.sorter === true) {
    _column.sorter = {
      multiple: 1
    }
  }

  columns.push(_column)
})

// 表头设置操作列
if (props.actions.length > 0) {
  columns.push({
    title: '',
    dataIndex: '_ACTIONS_'
  })
}

// 设置过滤器
const _filterOptions = []
const _filterValue = []
columns.forEach(i => {
  if (i.dataIndex === '_ACTIONS_') {
    return
  }

  _filterOptions.push({
    label: i.title,
    value: i.dataIndex
  })
  _filterValue.push(i.dataIndex)
})
const filter = ref({
  options: _filterOptions,
  value: _filterValue,
  open: false
})

const deselectFilter = value => {
  // 不允许过滤全部字段
  if (filteredColumns.value.length === 0 || (props.actions.length > 0 && filteredColumns.value.length === 1)) {
    filter.value.value.push(value)
  }
}

// 设置过滤表头
const filteredColumns = computed(() => {
  const _columns = []

  columns.forEach(i => {
    // 未被过滤则加入
    if (filter.value.value.indexOf(i.dataIndex) !== -1) {
      _columns.push(i)
    }

    // 加入操作列
    if (i.dataIndex === '_ACTIONS_') {
      _columns.push(i)
    }
  })

  return _columns
})

// 设置翻页器
const _pagination = {
  current: 1,
  pageSize: 10,
  pageSizeOptions: ['10', '20', '50', '100'],
  responsive: true,
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total, range) => `共${total}项记录，当前为第${range[0]}-${range[1]}项`,
  total: 0
}
extend(true, _pagination, props.pagination)
const pagination = props.pagination === false ? false : ref(_pagination)

const getCurrentPageIndex = () => {
  return pagination.value.current - 1
}

// 设置数据源
const dataSource = ref([])

// 设置加载器
const loading = ref(false)

// 翻页、排序时载入数据
const change = (pa, filters, sorters) => {
  pagination.value.pageSize = pa.pageSize

  let _sorters = sorters ? [].concat(sorters) : []
  _sorters = _sorters
    .filter(i => {
      return !isEmptyObject(i)
    })
    .map(i => {
      return {
        field: i.field,
        asc: i.order === 'ascend'
      }
    })

  loading.value = true
  props.load(pa === null ? -1 : pa.pageSize, pa === null ? 0 : pa.current - 1, _sorters)
    .then(page => {
      const _records = page.records || page.items

      _records.forEach(i => {
        columns.forEach(j => {
          if (!Object.prototype.hasOwnProperty.call(i, j.dataIndex)) {
            return
          }

          switch (j.type) {
            case 'datetime':
              i[j.dataIndex] = dayjs(i[j.dataIndex]).format(j.config.format)

              break

            case 'radio':
            case 'select':
            case '_switch':
              // eslint-disable-next-line no-case-declarations
              const _array = j.config.options.filter(k => {
                return i[j.dataIndex] === k.value
              })

              i[j.dataIndex] = _array.length > 0 ? _array[0].label : i[j.dataIndex]

              break
          }
        })
      })

      dataSource.value = _records

      // 设置分页
      if (pagination !== false) {
        pagination.value.current = pa.current
        pagination.value.total = page.total
      }
    })
    .finally(() => {
      loading.value = false
    })
}

// 根据页码载入数据
const load = page => {
  page = Number.isInteger(page) ? page + 1 : 1

  change({
    pageSize: pagination === false ? -1 : pagination.value.pageSize,
    current: page
  })
}

// 计算数据操作
const recordActions = (record) => {
  return Array.isArray(record._actions_)
    ? props.actions.filter((i, index) => {
      return record._actions_.indexOf(index) !== -1
    })
    : props.actions
}

const act = action => {
  let _aborted = true

  if (action.requireAny === true) {
    for (let i = 0; i < dataSource.value.length; i++) {
      if (dataSource.value[i]._ext_.selected) {
        _aborted = false
        break
      }
    }
  } else {
    _aborted = false
  }

  if (_aborted) {
    FeedbackUtil.message('您还未选择任何记录，请选择', 'warn')
    return
  }

  if (isFunction(action.callback)) {
    action.callback()
  }
}
const moreTool = ({ key }) => {
  act(props.toolbar[key - 0])
}

const moreAct = ({ key }, record) => {
  recordActions(record)[key - 0].callback(record)
}

// 暴露载入数据方法
defineExpose({
  load,
  getCurrentPageIndex,
  getSelectedRecords
})

onMounted(() => {
  if (props.loadAfterInitialized !== false) {
    load(0)
  }
})
</script>

<template>
  <!-- 工具栏 -->
  <a-space class="datatable-toolbar">
    <!-- ACTION -->
    <template v-if="Array.isArray(toolbar) && toolbar.length > 0">
      <a-space :size="8">
        <!-- 第一个按钮 -->
        <StatefulButton
          :icon="toolbar[0].icon"
          :type="'primary'"
          :on-click="() => act(toolbar[0])"
        >
          {{ toolbar[0].title }}
        </StatefulButton>

        <!-- 更多 -->
        <template v-if="toolbar.length > 1">
          <!-- 仅两个按钮，则不折叠 -->
          <template v-if="toolbar.length === 2">
            <StatefulButton
              :icon="toolbar[1].icon"
              :type="'primary'"
              :on-click="() => act(toolbar[1])"
            >
              {{ toolbar[1].title }}
            </StatefulButton>
          </template>
          <template v-else>
            <a-dropdown-button :trigger="'click'">
              更多
              <template #overlay>
                <a-menu @click="moreTool">
                  <template v-for="(i, index) in toolbar">
                    <template v-if="index > 0">
                      <a-menu-item :key="index">
                        <template #icon>
                          <GenericIcon :icon="i.icon" />
                        </template>
                        {{ i.title }}
                      </a-menu-item>
                    </template>
                  </template>
                </a-menu>
              </template>
            </a-dropdown-button>
          </template>
        </template>
      </a-space>
    </template>
    <!-- 在flex布局下仅占位 -->
    <template v-else>
      <div />
    </template>

    <!-- 过滤器 -->
    <a-input-group compact>
      <a-select
        v-model:value="filter.value"
        :max-tag-count="0"
        :max-tag-text-length="2"
        :mode="'multiple'"
        :options="filter.options"
        :show-arrow="true"
        :size="'middle'"
        @deselect="deselectFilter"
      >
        <template #suffixIcon>
          <icon-font
            :type="'icon-guolv'"
            style="font-size: 14px"
          />
        </template>
      </a-select>
    </a-input-group>
  </a-space>

  <a-table
    v-bind="$attrs"
    :bordered="true"
    :columns="filteredColumns"
    :data-source="dataSource"
    :loading="loading"
    :pagination="pagination"
    :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
    :row-key="rowKey"
    :row-selection="multiple ? { onChange: selected } : null"
    :size="size"
    @change="change"
  >
    <template #bodyCell="{ record, column }">
      <template v-if="column.dataIndex === '_ACTIONS_'">
        <template
          v-for="(i, index) in recordActions(record)"
          :key="index"
        >
          <!-- 仅完整显示前两个操作 -->
          <template v-if="index < 2">
            <!-- 按钮式 -->
            <!-- <StatefulButton :danger="i.danger || false"
                            :icon="i.icon"
                            :size="'small'"
                            :type="i.type || 'link'"
                            :on-click="() => i.callback(record)"
            >
              {{ i.title }}
            </StatefulButton> -->

            <a
              :style="i.danger ? { color: '#ff4d4f' } : null"
              @click="i.callback(record);"
            >
              {{ i.title }}
            </a>

            <!-- 分隔符 -->
            <template v-if="index !== Math.min(recordActions(record).length - 1, 1)">
              <a-divider :type="'vertical'" />
            </template>
          </template>
        </template>

        <template v-if="recordActions(record).length > 2">
          <!-- 分隔符 -->
          <a-divider :type="'vertical'" />

          <!-- 下拉列表 -->
          <div style="display: inline-block">
            <a-dropdown :trigger="'click'">
              <template #overlay>
                <a-menu @click="moreAct($event, record)">
                  <template v-for="(i, index) in recordActions(record)">
                    <template v-if="index > 1">
                      <a-menu-item :key="index">
                        <template #icon>
                          <GenericIcon :icon="i.icon" />
                        </template>
                        {{ i.title }}
                      </a-menu-item>
                    </template>
                  </template>
                </a-menu>
              </template>

              <!-- 按钮式 -->
              <!-- <a-button :type="'link'">
                更多
                <down-outlined />
              </a-button> -->

              <a>
                更多
                <down-outlined />
              </a>
            </a-dropdown>
          </div>
        </template>
      </template>

      <template v-else-if="column.type === 'image'">
        <a-image :src="record[column.dataIndex]" />
      </template>
    </template>

    <template #emptyText>
      <a-empty description="暂无数据" />
    </template>

    <template #buildOptionText="props">
      <span>{{ props.value }}条/页</span>
    </template>

    <template
      v-for="(index, name) in $slots"
      #[name]
    >
      <slot :name="name" />
    </template>
  </a-table>
</template>

<style lang="less">
@import '@/less/default';

.ant-space.datatable-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 18px;
  overflow-x: auto;
  overflow-y: hidden;
}

.ant-select-selection-overflow-item.ant-select-selection-overflow-item-suffix {
  display: none;
}

@media (max-width: @screen-xs) {
  .ant-table {
    width: 100%;
    overflow-x: auto;
  }
}
</style>
