<script setup>
import {onBeforeUnmount, onMounted, ref} from 'vue'
import AppApi from '@/api/sys/app.js'

onBeforeUnmount(() => {
  document.body.classList.remove('login-layout')
})

const title = ref('')
onMounted(() => {
  document.body.classList.add('login-layout')

  AppApi.getApp({
    toast: {
      success: false
    }
  }).then(result => {
    title.value = result.data.app
  })
})
</script>

<template>
  <div class="login-layout">
    <div class="login-layout-container">
      <div class="login-layout-container-content">
        <div class="top">
          <div class="header">
            <a>
              <img
                class="logo"
                src="@/assets/logo.png"
                alt=""
              >
            </a>
          </div>
          <div class="desc">
            {{ title }}
          </div>
        </div>

        <router-view class="main" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
.login-layout {
  height: 100%;

  .login-layout-container {
    width: 100%;
    min-height: 100%;
    background: #f0f2f5 url('@/assets/background.svg') no-repeat 50%;
    background-size: 100%;
    position: relative;

    .login-layout-container-content {
      padding: 32px 24px;

      > .top {
        text-align: center;

        > .header {
          .logo {
            height: 44px;
            vertical-align: top;
          }
        }

        > .desc {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.45);
          margin-top: 12px;
          margin-bottom: 40px;
        }
      }

      > .main {
        max-width: 368px;
        margin: 0 auto;
      }
    }
  }
}
</style>
