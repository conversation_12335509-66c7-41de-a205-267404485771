import HttpRequest from '@/utils/http.js'
import FileUtil from '@/utils/file.js'

const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL

export default {
  // 获取预览地址
  preview (id) {
    return id ? `${API_BASE_URL}/media/read?id=${id}` : null
  },
  // 获取播放地址
  play (id) {
    return id ? `${API_BASE_URL}/media/read/force-partial-content?id=${id}` : null
  },
  // 下载
  download (id) {
    new HttpRequest({
      responseType: 'blob'
    }).ajax(`/media/download?id=${id}`, null, {
      toast: {
        success: false
      }
    }).then(result => {
      FileUtil.copyInputStreamToFile(result.data.content, result.data.name)
    })
  },
  // 模糊搜索
  search (count, index, sortBy, path, name, temporary, options) {
    return new HttpRequest().ajax('/media/search', {
      count,
      index,
      sortBy,
      path,
      name,
      temporary
    }, options)
  },
  // 获取子路径
  subPath (path, options) {
    return new HttpRequest().ajax('/media/sub-paths', {
      path
    }, options)
  },
  // xhr上传文件
  uploadBody (bucket, files, options) {
    if (typeof bucket === 'string') {
      bucket = bucket.replaceAll(/\\/g, '/')
    }

    return new HttpRequest().ajax('/media/stage/base64', {
      bucket,
      records: files
    }, options)
  },
  // form上传文件
  uploadForm (bucket, files, options) {
    if (typeof bucket === 'string') {
      bucket = bucket.replaceAll(/\\/g, '/')
    }

    const _data = new FormData()

    files.forEach(i => {
      _data.append('files', i)
    })

    return new HttpRequest({
      headers: {
        'Content-Type': 'multipart/form-data',
        'X-Requested-With': ''
      } /*,
      onUploadProgress: function (event) {
        const _rate = event.loaded * 100 / event.total
      } */
    }).ajax(typeof bucket === 'string' ? `/media/stage/form?bucket=${bucket}` : '/media/stage/form', _data, options)
  },
  // 删除
  remove (id, force, options) {
    return new HttpRequest().ajax(force ? '/media/remove/force' : '/media/remove', {
      id
    }, options)
  }
}
