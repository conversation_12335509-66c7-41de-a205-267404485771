import {createApp} from 'vue'
import Antd from 'ant-design-vue'
import * as AntdIcons from '@ant-design/icons-vue'
import * as IconFont from '@/components/iconfont'
import '@/global.less'
import {router} from '@/router/index.js'
import Store from '@/store/index.js'
import App from '@/App.vue'

const app = createApp(App)

// 注册默认图标组件
for (const i in AntdIcons) {
  app.component(i, AntdIcons[i])
}

// 注册iconfont图标组件
app.component('IconFont', AntdIcons.createFromIconfontCN({
  scriptUrl: IconFont
}))

// 设置为全局属性
app.config.globalProperties.$icons = AntdIcons

app.use(Antd) // 安装ant-design-vue插件
  .use(router) // 安装路由插件
  .use(Store) // 安装状态管理插件
  .mount('#app')
