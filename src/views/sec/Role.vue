<script setup>
import {getCurrentInstance, ref} from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import RoleApi from '@/api/sec/role.js'

const { proxy } = getCurrentInstance()

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '名称',
    field: 'name',
    icon: 'UserOutlined',
    type: 'text'
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/sec/role',
    idField: 'id'
  },
  columns: [{
    title: '名称',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '描述',
    dataIndex: 'description'
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }],
  actions: [{
    title: '授权',
    icon: 'SafetyOutlined',
    callback (record) {
      proxy.$router.push({
        name: 'admin.sec.role.authorization',
        query: {
          id: record.id
        }
      })

      return Promise.resolve()
    }
  }, {
    title: '任命',
    icon: 'UserAddOutlined',
    callback (record) {
      proxy.$router.push({
        name: 'admin.sec.role.appointment',
        query: {
          id: record.id
        }
      })

      return Promise.resolve()
    }
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    RoleApi.search(count, index, sorters, _filters.name, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const remove = record => {
  return RoleApi.remove(record.id)
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <EdiTable
      ref="table"
      :mapper="tableOptions.mapper"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :addable="true"
      :load="load"
      :remove="remove"
    />
  </div>
</template>
