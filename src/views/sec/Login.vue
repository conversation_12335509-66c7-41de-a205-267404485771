<script setup>
import {getCurrentInstance, onMounted, ref, watch} from 'vue'
import FeedbackUtil from '@/utils/feedback.js'
import {pattern} from '@/utils/object-util.js'
import AppApi from '@/api/sys/app.js'
import LoginApi from '@/api/sec/login.js'

const { proxy } = getCurrentInstance()

// UI
const ui = ref({
  // 发送短信验证码
  send: {
    // 按钮状态
    value: 'ready',
    // 倒计时计时器
    timer: null,
    // 倒计时
    countdown: -1
  },
  // 登录
  login: {
    // 按钮状态
    value: 'ready'
  }
})

// 值
const model = ref({
  // 账号密码登录
  username: null,
  password: null,
  usernameAvailable: true,

  // 短信验证码登录
  mp: null,
  code: null,
  smsAvailable: true,

  // 图形验证码
  captchaToken: null,
  captchaImage: null,
  captchaText: null,

  // 记住我
  rememberMe: false
})

// 校验规则
const rules = ref({
  username: [{
    required: true,
    message: '请输入账号'
  }],
  password: [{
    required: true,
    message: '请输入密码'
  }],
  captchaText: [{
    required: true,
    message: '请输入图形验证码'
  }],
  mp: [{
    required: true,
    message: '请输入手机号码'
  }, {
    pattern: pattern.mp,
    message: '请输入有效的手机号码'
  }],
  code: [{
    required: true,
    message: '请输入短信验证码'
  }, {
    len: 6,
    message: '请输入6位短信验证码'
  }]
})

// 第三方平台
const platform = [{
  id: 'cmpassport',
  title: '手机号码',
  icon: 'icon-mobile',
  available: true
}, {
  id: 'wechat',
  title: '微信企业号',
  icon: 'icon-weixinworkqr',
  available: true
}, {
  id: 'yzy',
  title: '粤政易',
  icon: 'icon-yuezhengyi',
  available: true
}]

// 获取图形验证码
const getCaptcha = () => {
  LoginApi.captcha(109, 38, 4, {
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    model.value.captchaToken = result.data.token
    model.value.captchaImage = result.data.image
  })
}

// 表单
const form = ref()

// 发送短信验证码
const sendSms = () => {
  form.value.validate(['mp']).then(() => {
    ui.value.send.value = 'wait'

    LoginApi.sendCode(model.value.mp, {
      showLoading: false,
      toast: {
        success: false
      }
    })
      .then(result => {
        FeedbackUtil.notification('短信发送成功，请注意查收', 'success')

        // 设置倒计时
        if (Number.isFinite(result.data)) {
          ui.value.send.countdown = result.data

          // 设置定时器
          ui.value.send.timer = setInterval(() => {
            ui.value.send.countdown -= 1

            // 计时器到期则清除
            if (ui.value.send.countdown < 0) {
              clearInterval(ui.value.send.timer)
            }
          }, 1000)
        }
      })
      .finally(() => {
        ui.value.send.value = 'ready'
      })
  })
}

// 本地登录
const login = () => {
  let _fields
  switch (tab.value.activeKey) {
    case 'username':
      _fields = ['username', 'password', 'captchaText']
      break
    case 'sms':
      _fields = ['mp', 'code']
      break
    default:
      _fields = []
  }

  // 验证通过则登录
  form.value.validate(_fields)
    .then(() => {
      ui.value.login.value = 'wait'

      let _promise
      switch (tab.value.activeKey) {
        case 'username':
          _promise = LoginApi.loginByPassword(model.value.username, model.value.password, model.value.rememberMe, model.value.captchaText, model.value.captchaToken, {
            onUnauthenticated: {
              redirect: false
            },
            showLoading: false,
            toast: {
              success: false
            }
          })
          break
        case 'sms':
          _promise = LoginApi.loginBySms(model.value.mp, model.value.code, model.value.rememberMe, {
            onUnauthenticated: {
              redirect: false
            },
            showLoading: false,
            toast: {
              success: false
            }
          })
          break
      }

      _promise
        .then(() => {
          AppApi.getUser({
            showLoading: false,
            toast: {
              success: false
            }
          }).then(() => {
            FeedbackUtil.notification('登录成功，系统即将为您重定向……', 'success', {
              duration: 1,
              onClose () {
                redirect()
              }
            })
          })
        })
        .catch(() => {
          if (tab.value.activeKey === 'username') {
            getCaptcha()
          }
        })
        .finally(() => {
          ui.value.login.value = 'ready'
        })
    })
}

// OAuth2登录
const oauth2 = id => {
  switch (id) {
    case 'cmpassport':
      LoginApi.loginByCMPassport(redirectUrl, {
        onUnauthenticated: {
          redirect: false
        }
      })
        .then(() => redirect())
        .catch(error => {
          FeedbackUtil.modal('手机号码登录失败（' + error.message + '）', 'error')
        })

      break
    case 'wechat':
      LoginApi.loginByWx(window.location.origin + redirectUrl.value, {
        onUnauthenticated: {
          redirect: false
        },
        toast: {
          success: false
        }
      })
        .then(result => {
          window.location.href = result.data
        })
        .catch(error => {
          FeedbackUtil.modal('微信企业号登录失败（' + error.message + '）', 'error')
        })

      break
    case 'yzy':
      LoginApi.loginByYzy(window.location.origin + redirectUrl.value, {
        onUnauthenticated: {
          redirect: false
        },
        toast: {
          success: false
        }
      })
        .then(result => {
          window.location.href = result.data
        })
        .catch(error => {
          FeedbackUtil.modal('粤政易账号登录失败（' + error.message + '）', 'error')
        })

      break
  }
}

// 重定向地址
const redirectUrl = ref(null)
defineExpose({
  redirectUrl
})

// 重定向
const redirect = () => {
  typeof redirectUrl.value === 'string'
    ? proxy.$router.push({
      path: redirectUrl.value
    })
    : proxy.$router.push({
      name: 'admin.home'
    })
}

const tab = ref({
  activeKey: '',

  barStyle: {
    textAlign: 'center',
    borderBottom: 'unset'
  }
})

// 切换认证方式时修改表单校验规则
watch(
  () => tab.value.activeKey,
  value => {
    switch (value) {
      case 'username':
        form.value.clearValidate(['mp', 'code'])
        getCaptcha()

        break
      case 'sms':
        form.value.clearValidate(['username', 'password', 'captcha'])
        break
    }
  }
)

onMounted(() => {
  tab.value.activeKey = 'username'
})
</script>

<script>
export default {
  beforeRouteEnter: function (to, from, next) {
    const redirectUrl = to.query && to.query.redirect ? to.query.redirect : from.fullPath

    next(vm => {
      // 设置重定向地址
      vm.redirectUrl = redirectUrl
    })
  }
}
</script>

<template>
  <div>
    <a-form
      ref="form"
      :model="model"
      :scroll-to-first-error="true"
      :validate-on-rule-change="true"
      @keydown.enter="login"
    >
      <a-tabs
        v-model:activeKey="tab.activeKey"
        :tab-bar-style="tab.barStyle"
        centered
      >
        <!-- 账号密码登录-->
        <a-tab-pane
          v-if="model.usernameAvailable"
          :key="'username'"
          :tab="'账号密码登录'"
        >
          <a-form-item
            :rules="rules.username"
            name="username"
          >
            <a-input
              v-model:value="model.username"
              :placeholder="'账号'"
              :size="'large'"
              :type="'text'"
            >
              <template #prefix>
                <user-outlined class="input-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            :rules="rules.password"
            name="password"
          >
            <a-input-password
              v-model:value="model.password"
              :autocomplete="true"
              :placeholder="'密码'"
              :size="'large'"
            >
              <template #prefix>
                <lock-outlined class="input-icon" />
              </template>
            </a-input-password>
          </a-form-item>

          <a-row :gutter="8">
            <a-col :span="16">
              <a-form-item
                :rules="rules.captchaText"
                name="captchaText"
              >
                <a-input
                  v-model:value="model.captchaText"
                  :placeholder="'图形验证码'"
                  :size="'large'"
                  :type="'text'"
                />
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-image
                :preview="false"
                :src="model.captchaImage"
                style="cursor: pointer;"
                @click="getCaptcha"
              />
            </a-col>
          </a-row>
        </a-tab-pane>

        <!-- 短信验证码登录 -->
        <a-tab-pane
          v-if="model.smsAvailable"
          :key="'sms'"
          :tab="'短信验证码登录'"
        >
          <a-form-item
            :rules="rules.mp"
            name="mp"
          >
            <a-input
              v-model:value="model.mp"
              :placeholder="'手机号码'"
              :size="'large'"
              :type="'text'"
            >
              <template #prefix>
                <mobile-outlined class="input-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-row :gutter="8">
            <a-col :span="16">
              <a-form-item
                :rules="rules.code"
                name="code"
              >
                <a-input
                  v-model:value="model.code"
                  :placeholder="'短信验证码'"
                  :size="'large'"
                  :type="'text'"
                >
                  <template #prefix>
                    <mail-outlined class="input-icon" />
                  </template>
                </a-input>
              </a-form-item>
            </a-col>
            <a-col :span="8">
              <a-button
                :disabled="ui.send.value === 'wait' || ui.send.countdown >= 0"
                class="sms-code"
                @click.stop.prevent="sendSms"
              >
                <template v-if="ui.send.value === 'ready' && ui.send.countdown < 0">
                  <a>获取验证码</a>
                </template>
                <template v-else>
                  {{ ui.send.countdown >= 0 ? ui.send.countdown + '秒后重试' : '' }}
                </template>
              </a-button>
            </a-col>
          </a-row>
        </a-tab-pane>
      </a-tabs>

      <!-- 记住我 -->
      <a-form-item>
        <a-checkbox v-model:checked="model.rememberMe">
          记住我
        </a-checkbox>
      </a-form-item>

      <a-form-item style="margin-top:24px">
        <a-button
          :loading="ui.login.value === 'wait'"
          :size="'large'"
          :type="'primary'"
          class="login"
          @click="login"
        >
          登录
        </a-button>
      </a-form-item>

      <div class="login-oauth2">
        <span>其它登录方式</span>
        <template
          v-for="(i, index) in platform"
          :key="index"
        >
          <a-popconfirm
            :cancel-text="'取消'"
            :ok-text="'确定'"
            :placement="top"
            :title="'您即将使用' + i.title + '登录，是否继续？'"
            @confirm="oauth2(i.id);"
          >
            <template #icon>
              <question-circle-outlined />
            </template>
            <a>
              <icon-font
                :style="i.icon === 'icon-yuezhengyi' ? {'font-size': '18px'} : null"
                :type="i.icon"
                class="item-icon"
              />
            </a>
          </a-popconfirm>
        </template>
        <!-- <router-link :to="{ name: 'sec.register' }"
                     class="register"
        >
          注册账户
        </router-link> -->
      </div>
    </a-form>
  </div>
</template>

<style lang="less" scoped>
@namespace xlink 'http://www.w3.org/1999/xlink';

button > a {
  color: #1890ff !important;
}

.input-icon {
  color: rgba(0, 0, 0, .25);
}

button.login {
  padding: 0 15px;
  font-size: 16px;
  height: 40px;
  width: 100%;
}

button.sms-code {
  width: 100%;
  height: 40px;
}

.login-oauth2 {
  text-align: left;
  margin-top: 24px;
  line-height: 22px;

  .item-icon {
    font-size: 24px;
    color: rgba(0, 0, 0, 0.2);
    margin-left: 16px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;
  }

  .register {
    float: right;
  }
}
</style>
