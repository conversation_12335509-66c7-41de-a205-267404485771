<script setup>
import {onMounted, ref} from 'vue'
import AppApi from '@/api/sys/app.js'
import StatisticApi from '@/api/sys/statistic.js'

const gutter = [8, 8]

const span = {
  xs: 24,
  sm: 12,
  xl: 6
}

const ready = ref(false)

const welcome = ref({
  greeting: null,
  user: {
    name: '',
    department: '',
    avatar: null
  }
})

const indicator = ref({
  ru: 0,
  cu: 0,
  dar: 0,
  urr: 0
})

onMounted(() => {
  ready.value = true

  const _hour = new Date().getHours()
  if (_hour < 6) {
    welcome.value.greeting = '辛苦了'
  } else if (_hour < 12) {
    welcome.value.greeting = '早安'
  } else if (_hour < 18) {
    welcome.value.greeting = '午安'
  } else {
    welcome.value.greeting = '晚安'
  }

  AppApi.getUser({
    showLoading: false,
    toast: {
      success: false,
      error: false
    }
  }).then(result => {
    welcome.value.user = {
      name: result.data.name,
      department: result.data.deptFullName,
      avatar: result.data.avatarUrl || 'https://gw.alipayobjects.com/zos/antfincdn/XAosXuNZyF/BiazfanxmamNRoxxVxka.png'
    }
  })

  StatisticApi.operation({
    showLoading: false,
    toast: {
      success: false
    }
  }).then(result => {
    indicator.value.ru = result.data.ru
    indicator.value.cu = result.data.cu
    indicator.value.dar = result.data.dar * 10000 / 100.00
    indicator.value.urr = result.data.urr * 10000 / 100.00
  })
})
</script>

<template>
  <template v-if="ready">
    <teleport to=".ant-tabs-tabpane-active">
      <a-page-header>
        <div class="home-page-header">
          <div>
            <div class="home-page-header-content">
              <a-avatar
                :size="70"
                :src="welcome.user.avatar"
              />
              <div class="home-page-header-content-heading">
                <div class="home-page-header-content-heading-title">
                  {{ welcome.greeting }}，
                  <template v-if="typeof welcome.user.name === 'string' && welcome.user.name">
                    <span style="color: rgba(248,113,113)">{{ welcome.user.name }}</span>，
                  </template>
                  祝您开心每一天！
                </div>
                <div class="home-page-header-content-heading-sub-title">
                  <bank-outlined /> {{ welcome.user.department }}
                </div>
              </div>
            </div>
          </div>
          <div>
            <!-- 个人统计指标 -->
            <div class="home-page-header-extra">
              <a-statistic
                :title="'项目'"
                :value="0"
              />
              <a-divider :type="'vertical'" />
              <a-statistic
                :title="'任务'"
                :value="0"
              />
              <a-divider :type="'vertical'" />
              <a-statistic
                :title="'通知'"
                :value="0"
              />
            </div>
          </div>
        </div>
      </a-page-header>
    </teleport>
  </template>

  <div class="operation-container">
    <a-row
      :align="'middle'"
      :gutter="gutter"
    >
      <a-col
        :sm="span.sm"
        :xl="span.xl"
        :xs="span.xs"
      >
        <a-card :body-style="{ 'background-color': '#3598dc', 'border-radius': '4px' }">
          <a-statistic
            :title="'注册用户'"
            :value="indicator.ru + '人'"
            :value-style="{ color: '#fff' }"
            style="margin-right: 50px"
          >
            <template #suffix>
              <team-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col
        :sm="span.sm"
        :xl="span.xl"
        :xs="span.xs"
      >
        <a-card :body-style="{ 'background-color': '#e7505a', 'border-radius': '4px' }">
          <a-statistic
            :title="'在线用户'"
            :value="indicator.cu + '人'"
            :value-style="{ color: '#fff' }"
            style="margin-right: 50px"
          >
            <template #suffix>
              <desktop-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col
        :sm="span.sm"
        :xl="span.xl"
        :xs="span.xs"
      >
        <a-card :body-style="{ 'background-color': '#32c5d2', 'border-radius': '4px' }">
          <a-statistic
            :title="'日活跃率'"
            :value="indicator.dar + '%'"
            :value-style="{ color: '#fff' }"
            style="margin-right: 50px"
          >
            <template #suffix>
              <arrow-up-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>

      <a-col
        :sm="span.sm"
        :xl="span.xl"
        :xs="span.xs"
      >
        <a-card :body-style="{ 'background-color': '#8E44AD', 'border-radius': '4px' }">
          <a-statistic
            :title="'用户留存率'"
            :value="indicator.urr + '%'"
            :value-style="{ color: '#fff' }"
            style="margin-right: 50px"
          >
            <template #suffix>
              <history-outlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<style lang="less">
.home-page-header {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 100%;

  > div:first-child {
    margin-bottom: 16px;
  }

  .ant-avatar {
    flex-shrink: 0;
  }

  .home-page-header-content {
    display: flex;

    .home-page-header-content-heading {
      flex: 1 1 auto;
      margin-left: 24px;
      line-height: 22px;

      .home-page-header-content-heading-title {
        margin-bottom: 12px;
        font-size: 20px;
        font-weight: 500;
        line-height: 28px
      }

      .home-page-header-content-heading-sub-title {
        color: rgba(0, 0, 0, 0.65);
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
}

.home-page-header-extra {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .ant-statistic {
    padding: 0 32px;
  }

  .ant-statistic:last-child {
    padding-right: 0;
  }

  .ant-divider {
    height: 36px;
  }
}

.operation-container {
  .ant-statistic {
    margin-right: 0 !important;

    .ant-statistic-title {
      color: rgba(255, 255, 255, 0.7);
    }

    span.ant-statistic-content-suffix {
      float: right;
    }
  }
}
</style>
