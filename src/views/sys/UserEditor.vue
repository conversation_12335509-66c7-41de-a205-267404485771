<script setup>
import {getCurrentInstance, onMounted, ref} from 'vue'
import ProfileEditor from '@/components/ProfileEditor.vue'
import UserApi from '@/api/sys/user.js'

const { proxy } = getCurrentInstance()

const get = () => {
  return proxy.$route.query.id
    ? UserApi.get(proxy.$route.query.id, {
      toast: {
        success: false
      }
    })
    : Promise.resolve({
      code: 'OK',
      data: null
    })
}

const save = model => {
  return UserApi.save(model)
}

const editor = ref()
onMounted(() => {
  // 重置字段可编辑
  editor.value.fields
    .filter(i => {
      return ['name', 'account', 'department', 'alphabet'].indexOf(i.field) !== -1
    })
    .forEach(i => {
      i.config.disabled = false
    })
})
</script>

<template>
  <ProfileEditor
    ref="editor"
    :get="get"
    :save="save"
  />
</template>
