<script setup>
import {onMounted, ref} from 'vue'
import StatefulButton from '@/components/StatefulButton.vue'
import StatefulModal from '@/components/StatefulModal.vue'
import JobApi from '@/api/sys/job.js'

const table = ref({
  columns: [{
    title: '作业',
    dataIndex: 'jobName',
    align: 'center'
  }, {
    title: '分组',
    dataIndex: 'jobGroup',
    align: 'center'
  }, {
    title: '描述',
    dataIndex: 'jobDesc',
    align: 'center'
  }, {
    title: '计划任务',
    dataIndex: 'triggerCronExpression',
    align: 'center'
  }, {
    title: '状态',
    dataIndex: 'triggerState',
    align: 'center'
  }, {
    title: '',
    dataIndex: '_ACTION_',
    align: 'center'
  }],
  dataSource: [],
  loading: false
})

const load = () => {
  table.value.loading = true

  const _promise = JobApi.load({
    showLoading: false,
    toast: {
      success: false
    }
  })

  _promise
    .then(result => {
      table.value.dataSource = result.data
    })
    .finally(() => {
      table.value.loading = false
    })

  return _promise
}

const triggering = record => {
  const _promise = JobApi.trigger(record.jobName, record.jobGroup)

  _promise.then(() => load())

  return _promise
}

const toggleState = record => {
  const _promise = record.triggerState === 'PAUSED' ? JobApi.resume(record.jobName, record.jobGroup) : JobApi.pause(record.jobName, record.jobGroup)

  _promise.then(() => load())

  return _promise
}

const modal = ref()

const buttons = [{
  title: '确定',
  icon: 'CheckOutlined',
  type: 'primary',
  onClick () {
    return updateCron()
  }
}]

const editTrigger = record => {
  trigger.value = {
    name: record.triggerName,
    group: record.triggerGroup,
    cron: record.triggerCronExpression
  }

  modal.value.show()
}

const onClose = () => {
  trigger.value = {
    name: null,
    group: null,
    cron: null
  }
}

const form = ref()

const trigger = ref({
  name: null,
  group: null,
  cron: null
})

const updateCron = () => {
  return new Promise(resolve => {
    form.value.validate(['cron'])
      .then(() => {
        JobApi.updateCron(trigger.value.name, trigger.value.group, trigger.value.cron)
          .then(() => {
            load()

            modal.value.open = false
          })
          .finally(() => {
            resolve()
          })
      })
      .catch(({ errorFields, outOfDate, values }) => {
        resolve()
      })
  })
}

onMounted(() => {
  load()
})
</script>

<template>
  <!-- 列表 -->
  <a-table
    :columns="table.columns"
    :data-source="table.dataSource"
    :loading="table.loading"
    :pagination="false"
    :size="'small'"
  >
    <template #headerCell="{ column }">
      <template v-if="column.dataIndex === '_ACTION_'">
        <StatefulButton
          :icon="'ReloadOutlined'"
          :shape="'circle'"
          :title="'刷新'"
          :type="'link'"
          :on-click="load"
        />
      </template>
    </template>

    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === '_ACTION_'">
        <a-space>
          <a-button
            :shape="'circle'"
            :title="'更新计划'"
            @click="editTrigger(record)"
          >
            <template #icon>
              <clock-circle-outlined />
            </template>
          </a-button>

          <StatefulButton
            :icon="'PlayCircleOutlined'"
            :shape="'circle'"
            :title="'立即执行'"
            :type="'primary'"
            :on-click="() => triggering(record)"
          />

          <template v-if="record.triggerState === 'NORMAL'">
            <StatefulButton
              :danger="true"
              :icon="'PauseCircleOutlined'"
              :shape="'circle'"
              :title="'暂停'"
              :type="'primary'"
              :on-click="() => toggleState(record)"
            />
          </template>
          <template v-else-if="record.triggerState === 'PAUSED'">
            <StatefulButton
              :icon="'UndoOutlined'"
              :shape="'circle'"
              :title="'恢复'"
              :type="'primary'"
              :on-click="() => toggleState(record)"
            />
          </template>
        </a-space>
      </template>
    </template>

    <template #emptyText>
      <a-empty description="暂无数据" />
    </template>
  </a-table>

  <!-- 编辑器 -->
  <StatefulModal
    ref="modal"
    :buttons="buttons"
    :title="'计划任务'"
    @close="onClose"
  >
    <a-form
      ref="form"
      :model="trigger"
    >
      <a-form-item
        :rules="[{ required: true, message: '请输入CRON表达式' }]"
        name="cron"
      >
        <a-input
          v-model:value="trigger.cron"
          placeholder="请输入CRON表达式"
        >
          <template #prefix>
            <calendar-outlined />
          </template>
        </a-input>
      </a-form-item>
    </a-form>
  </StatefulModal>
</template>
