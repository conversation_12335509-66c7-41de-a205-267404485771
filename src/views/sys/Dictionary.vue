<script setup>
import {ref} from 'vue'
import EdiTable from '@/components/EdiTable.vue'
import SearchBar from '@/components/SearchBar.vue'
import DictionaryApi from '@/api/sys/dictionary.js'

const _statusOptions = [{
  label: '启用',
  value: true
}, {
  label: '禁用',
  value: false
}]

const searchbar = ref()
const searchbarOptions = {
  fields: [{
    title: '分组',
    field: 'groupId',
    icon: 'FolderOutlined',
    type: 'text'
  }, {
    title: '键',
    field: 'name',
    icon: 'TagOutlined',
    type: 'text'
  }, {
    title: '状态',
    field: 'enabled',
    type: 'select',
    config: {
      options: _statusOptions
    }
  }],
  actions: [{
    title: '搜索',
    icon: 'SearchOutlined',
    callback () {
      table.value.load()
    }
  }]
}

const table = ref()
const tableOptions = {
  mapper: {
    path: '/sys/dictionary',
    idField: 'id'
  },
  columns: [{
    title: '分组',
    dataIndex: 'groupId',
    sorter: true
  }, {
    title: '键',
    dataIndex: 'name',
    sorter: true
  }, {
    title: '值',
    dataIndex: 'val',
    sorter: true
  }, {
    title: '排序',
    dataIndex: 'seq',
    sorter: true
  }, {
    title: '内置变量',
    dataIndex: 'buildIn'
  }, {
    title: '只读变量',
    dataIndex: 'readonly'
  }, {
    title: '描述',
    dataIndex: 'description'
  }, {
    title: '状态',
    dataIndex: 'isEnabled',
    type: 'select',
    config: {
      options: _statusOptions
    }
  }, {
    title: '创建人员',
    dataIndex: 'creatorName'
  }, {
    title: '创建时间',
    dataIndex: 'createTime',
    sorter: true
  }]
}

const load = (count, index, sorters) => {
  const _filters = searchbar.value.model()

  return new Promise(resolve => {
    DictionaryApi.search(count, index, sorters, _filters.groupId, _filters.name, _filters.enabled, {
      showLoading: false,
      toast: {
        success: false
      }
    }).then(result => {
      resolve(result.data)
    }).catch(() => {
      resolve({
        total: 0,
        records: []
      })
    })
  })
}

const remove = record => {
  return DictionaryApi.remove(record.id)
}
</script>

<template>
  <div class="layout-content-panel">
    <SearchBar
      ref="searchbar"
      :fields="searchbarOptions.fields"
      :actions="searchbarOptions.actions"
    />
  </div>

  <div class="layout-content-panel">
    <EdiTable
      ref="table"
      :mapper="tableOptions.mapper"
      :actions="tableOptions.actions"
      :columns="tableOptions.columns"
      :addable="true"
      :load="load"
      :remove="remove"
    />
  </div>
</template>
